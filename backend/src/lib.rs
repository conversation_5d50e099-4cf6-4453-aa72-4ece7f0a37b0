extern crate alloc;

pub mod config;
mod controller;
mod model;
mod repository;
pub mod service;
mod utils;
mod web_server;

mod middleware;
mod routes_builder;

use crate::config::config::GlobalConfig;
use crate::config::grading_config::GradingConfig;
use crate::config::task_config::TaskConfig;
use crate::config::MinioConfig;

use crate::service::administrative_classes::administrative_classes_service::AdministrativeClassesService;
use crate::service::classes::classes_service::ClassesService;
use crate::service::education_stage::EducationStageService;
use crate::service::grade::grade_service::GradeService;
use crate::service::homework::homework_service::HomeworkService;
use crate::service::menu::MenuService;
use crate::service::paper::paper::PaperService;
use crate::service::paper_scan_page::paper_scan_page_service::PaperScanPagesService;
use crate::service::paper_scans::paper_scans_service::PaperScansService;
use crate::service::permission::MultiTenantCasbinService;
use crate::service::question::question_type_service::QuestionTypeService;
use crate::service::role::role_service::RoleService;
use crate::service::storage::{MinioStorageService, FallbackStorageService, StorageService};
use crate::service::student::{StudentService, StudentImportService};
use crate::service::subject::SubjectService;
use crate::service::subject_groups::subject_groups_service::SubjectGroupsService;
use crate::service::task_queue::TaskQueueState;
use crate::service::teacher::teacher_service::TeacherService;
use crate::service::teacher::import_service::TeacherImportService;
use crate::service::teaching_aids::teaching_aids_service::TeachingAidsService;
use crate::service::teaching_aids::textbook_paper_service::TextbookPaperService;
use crate::service::teaching_classes::teaching_classes_service::TeachingClassesService;
use crate::service::auth::auth_service::AuthService;
use crate::service::user::{UserService, IdentityService};
use crate::service::user::parent_service::ParentService;
use crate::service::sms::SmsService;
use crate::utils::password::PasswordService;
use crate::repository::user::identities::user_identity_repository::UserIdentityRepository;
use crate::repository::user::parent::ParentRepository;
use crate::service::workflow::workflow_service::WorkflowService;
use crate::utils::redis_client::get_redis_client;
use crate::web_server::build_app;
use crate::web_server::AppState;
use dotenvy::dotenv;
use service::homework::homework_students_service::HomeworkStudentsService;
use service::tenant::user::tenant_user_service::TenantUserService;
use service::tenant::tenant_service::TenantService;
use sqlx::postgres::PgPoolOptions;
use std::{env, net::SocketAddr, sync::Arc};
use tracing::Level;
use tracing::{error, info};

pub async fn run_server() -> anyhow::Result<()> {
    // 1. 初始化日志系统
    tracing_subscriber::fmt().with_target(true).with_line_number(true).with_max_level(Level::INFO).compact().init();

    info!("🚀 Starting Deep-Mate Backend Server");

    // 2. 加载 .env
    dotenv().ok();

    // 3. 初始化数据库连接池
    let db_url = env::var("DATABASE_URL")?;
    info!("📊 Connecting to database...");

    let pool = PgPoolOptions::new()
        .max_connections(20) // Increased for better performance
        .connect(&db_url)
        .await
        .map_err(|e| {
            error!("❌ Failed to connect to database: {}", e);
            e
        })?;

    info!("✅ Database connection established");

    // 4. 运行迁移
    info!("🔄 Running database migrations...");
    sqlx::migrate!().run(&pool).await.map_err(|e| {
        error!("❌ Failed to run migrations: {}", e);
        e
    })?;
    info!("✅ Database migrations completed");

    // 5. 初始化认证相关服务
    info!("🔐 Initializing authentication services...");

    // Initialize core services
    let sms_service = Arc::new(SmsService::new());
    let password_service = Arc::new(PasswordService::new());

    // Initialize business services
    let auth_service = Arc::new(AuthService::new(
        pool.clone(),
        sms_service.clone(),
        password_service.clone(),
    ));

    // Initialize repository and identity service
    let user_identity_repository = Arc::new(UserIdentityRepository::new(pool.clone()));
    let identity_service = Arc::new(IdentityService::new(pool.clone(), user_identity_repository));

    // Initialize parent repository and service
    let parent_repository = Arc::new(ParentRepository::new(pool.clone()));
    let parent_service = Arc::new(ParentService::new(pool.clone(), parent_repository));

    let tenant_service = Arc::new(TenantService::new(pool.clone()));

    info!("✅ Authentication services initialized");

    // 6. 加载 Schema 配置
    let schema_config = config::get_schema_config();
    info!(
        "📋 Schema config loaded: warmup_strategy={:?}, auto_create={}",
        schema_config.warmup_strategy, schema_config.auto_create_missing_schemas
    );

    // 7. 初始化 MinIO 存储服务
    info!("📁 Initializing storage service...");
    let minio_config = MinioConfig::from_env().map_err(|e| {
        error!("❌ Failed to load MinIO configuration: {}", e);
        e
    })?;
    let storage_service: Arc<dyn StorageService> = match MinioStorageService::new(minio_config.clone()).await {
        Ok(service) => {
            info!("✅ Storage service initialized");
            Arc::new(service)
        }
        Err(e) => {
            error!("⚠️ Failed to initialize MinIO storage service: {}, using fallback", e);
            Arc::new(FallbackStorageService::new())
        }
    };

    // 8. 初始化阅卷服务配置
    info!("📁 Initializing grading service...");
    let grading_config = GradingConfig::from_env().map_err(|e| {
        error!("❌ Failed to load Grading configuration: {}", e);
        e
    })?;

    info!("✅ Grading service initialized");

    // 9. 初始化 ClassService、RoleService、SubjectService、GradeService、StudentService、TeacherService 和 EducationStageService
    let classes_service = Arc::new(ClassesService::new(pool.clone()));
    let administrative_classes_service = Arc::new(AdministrativeClassesService::new(pool.clone()));
    let teaching_classes_service = Arc::new(TeachingClassesService::new(pool.clone()));
    let subject_groups_service = Arc::new(SubjectGroupsService::new(pool.clone()));
    let user_service = UserService::new(pool.clone(), password_service.clone());
    let role_service = RoleService::new(pool.clone());
    let subject_service = SubjectService::new(pool.clone());
    let grade_service = GradeService::new(pool.clone());
    let student_service = StudentService::with_default_filter(pool.clone());
    let teacher_service = TeacherService::new(pool.clone());
    let tenant_user_service = Arc::new(TenantUserService::new(pool.clone()));
    let student_import_service = StudentImportService::new(
        pool.clone(),
        Arc::new(student_service.clone()),
        user_service.clone(),
        tenant_user_service.clone(),
        (*tenant_service).clone(),
        role_service.clone(),
        administrative_classes_service.clone(),
    );
    let teacher_import_service = TeacherImportService::new(
        pool.clone(),
        teacher_service.clone(),
        subject_service.clone(),
        grade_service.clone(),
        subject_groups_service.clone(),
        administrative_classes_service.clone(),
        teaching_classes_service.clone(),
        Arc::new(student_service.clone()),
        user_service.clone(),
        tenant_user_service.clone(),
        (*tenant_service).clone(),
        role_service.clone(),
    );
    let homework_service = Arc::new(HomeworkService::new(pool.clone()));
    let homework_students_service = Arc::new(HomeworkStudentsService::new(pool.clone()));
    let education_stage_service = EducationStageService::new(pool.clone());
    let question_type_service = Arc::new(QuestionTypeService::new(pool.clone()));
    let paper_scans_service = PaperScansService::new(pool.clone(), student_service.clone());
    let paper_scan_page_service = PaperScanPagesService::new(pool.clone());

    let config = Arc::new(GlobalConfig::new(minio_config.clone(), grading_config.clone(), schema_config.clone()));
    let workflow_service = Arc::new(WorkflowService::new(pool.clone()));
    let paper_service = Arc::new(PaperService::new(pool.clone()));
    let textbook_paper_service = Arc::new(TextbookPaperService::new(pool.clone()));
    let teaching_aids_service = Arc::new(TeachingAidsService::new(pool.clone(), storage_service.clone(), paper_service.clone(), textbook_paper_service.clone()));
    let redis_pool = get_redis_client().await?;
    let task_config = TaskConfig::from_env();
    let task_queue = TaskQueueState::new(pool.clone(), redis_pool, storage_service.clone(), task_config);
    task_queue.run();
    let menu_service = Arc::new(MenuService::new(pool.clone()));
    // 10. 构建 App 路由

    // 10. 初始化 Casbin 权限服务
    info!("🔐 Initializing Casbin permission service...");
    let casbin_service = Arc::new(
        MultiTenantCasbinService::new(pool.clone(), "config/rbac_model.conf".to_string(), menu_service.clone())
            .await
            .map_err(|e| {
                error!("❌ Failed to initialize Casbin service: {}", e);
                anyhow::anyhow!("Casbin initialization failed: {}", e)
            })?,
    );
    info!("✅ Casbin permission service initialized");



    // 12. 用户身份管理服务已合并到 AuthIntegration 中的 IdentityService
    info!("✅ User identity service integrated into IdentityService");

    // 13. 构建 App 路由
    let state = AppState {
        db: pool,
        config,
        storage_service,
        auth_service: auth_service.clone(),
        identity_service: identity_service.clone(),
        parent_service: parent_service.clone(),
        sms_service: sms_service.clone(),
        password_service: password_service.clone(),
        tenant_service: tenant_service.clone(),
        casbin_service: casbin_service.clone(),
        classes_service: classes_service.clone(),
        administrative_classes_service: administrative_classes_service.clone(),
        teaching_classes_service: teaching_classes_service.clone(),
        subject_groups_service: subject_groups_service.clone(),
        user_service,
        role_service,
        subject_service,
        grade_service,
        student_service,
        student_import_service,
        teacher_service,
        teacher_import_service,
        homework_service,
        homework_students_service,
        education_stage_service,
        teaching_aids_service,
        question_type_service,
        workflow_service,
        paper_service,
        textbook_paper_service,
        task_queue,
        paper_scans_service,
        paper_scan_page_service,
        menu_service,
        tenant_user_service,
    };
    let app = build_app(state);

    // 11. 启动服务
    let port = env::var("PORT").unwrap_or_else(|_| "3000".to_string()).parse::<u16>().unwrap_or(3000);

    let addr = SocketAddr::from(([0, 0, 0, 0], port));
    info!("🌐 Server starting on http://{}", addr);

    let listener = tokio::net::TcpListener::bind(addr).await.map_err(|e| {
        error!("❌ Failed to bind to address {}: {}", addr, e);
        e
    })?;

    info!("✅ Deep-Mate Backend Server is running on http://{}", addr);
    info!("📚 API Documentation available at http://{}/docs", addr);

    axum::serve(listener, app.into_make_service_with_connect_info::<SocketAddr>()).await.map_err(|e| {
        error!("❌ Server error: {}", e);
        e
    })?;

    Ok(())
}
