use anyhow::anyhow;
use sqlx::{pool::PoolConnection, PgPool, Postgres, QueryBuilder};
use tracing::debug;
use uuid::Uuid;
use crate::model::{homework::homework::Homework, PageParams};
use crate::service::permission::{CasbinPermissionService, DataFilterManager, FilterContext};

/**
 * 作者：张瀚
 * 说明：作业表的数据库方法
 */
pub struct HomeworkRepository {}

impl HomeworkRepository {
    /**
     * 作者：张瀚
     * 说明：分页查询所有作业
     * homework_id_in_list:不传代表是管理员，可以看所有的，如果传了就只能查到这个范围内的作业
     */
    pub async fn page_all_homework(
        conn: &mut PoolConnection<Postgres>,
        page_params: &PageParams,
        homework_id_in_list: Option<Vec<Uuid>>,
        name: &Option<String>,
        status: &Option<String>,
        subject_group_id: &Option<Uuid>,
    ) -> Result<(Vec<Homework>, i64), String> {
        if let Some(id_list) = homework_id_in_list {
            return Self::query_homework_by_ids(conn, page_params, &id_list).await;
        }
        //select * from homework h where h.name like '%' and h.status = '' and h.subject_group_id = '' order by ... limit ...
        // 基础查询语句
        let mut query_builder = QueryBuilder::new("SELECT h.* FROM homework h WHERE 1=1");
        let mut count_builder = QueryBuilder::new("SELECT count(*) FROM homework h WHERE 1=1");
        //添加查询条件
        if let Some(name_val) = name {
            if !name_val.trim().is_empty() {
                query_builder.push(" and h.homework_name like ");
                query_builder.push_bind(format!("%{}%", name_val));

                count_builder.push(" and h.homework_name like ");
                count_builder.push_bind(format!("%{}%", name_val));
            }
        }

        if let Some(status_val) = status {
            if !status_val.trim().is_empty() {
                query_builder.push(" and h.homework_status = ");
                query_builder.push_bind(status_val);
                count_builder.push(" and h.homework_status = ");
                count_builder.push_bind(status_val);
            }
        }

        if let Some(subject_group_id_val) = subject_group_id {
            if !subject_group_id_val.is_nil() {
                query_builder.push(" and h.subject_group_id = ");
                query_builder.push_bind(subject_group_id_val);
                count_builder.push(" and h.subject_group_id = ");
                count_builder.push_bind(subject_group_id_val);
            }
        }

        //添加分页和排序
        query_builder.push(" order by h.created_at desc limit ");
        query_builder.push_bind(page_params.get_limit() as i32);
        query_builder.push(" offset ");
        query_builder.push_bind(page_params.get_offset() as i32);
        //执行sql
        let list = query_builder.build_query_as::<Homework>().fetch_all(conn.as_mut()).await.map_err(|e| e.to_string())?;
        let count = count_builder.build_query_scalar().fetch_one(conn.as_mut()).await.map_err(|e| e.to_string())?;

        Ok((list, count))
    }

    /**
     * 作者：张瀚 (重构)
     * 说明：根据ID列表查询作业（支持分页）
     */
    async fn query_homework_by_ids(conn: &mut PoolConnection<Postgres>, page_params: &PageParams, id_list: &[Uuid]) -> Result<(Vec<Homework>, i64), String> {
        if id_list.is_empty() {
            return Ok((Vec::new(), 0));
        }

        // 使用 QueryBuilder 安全构建 IN 子句
        let mut query_builder = QueryBuilder::new("SELECT h.* FROM homework h WHERE h.id IN (");
        let mut count_builder = QueryBuilder::new("SELECT COUNT(*) FROM homework h WHERE h.id IN (");
        
        let mut separated = query_builder.separated(", ");
        let mut count_separated = count_builder.separated(", ");

        for id in id_list {
            separated.push_bind(id);
            count_separated.push_bind(id);
        }

        query_builder.push(") ORDER BY h.created_at DESC LIMIT ");
        query_builder.push_bind(page_params.get_limit() as i32);
        query_builder.push(" OFFSET ");
        query_builder.push_bind(page_params.get_offset() as i32);

        count_builder.push(")");

        // 执行查询
        let list = query_builder.build_query_as::<Homework>().fetch_all(conn.as_mut()).await.map_err(|e| e.to_string())?;

        let count: i64 = count_builder.build_query_scalar().fetch_one(conn.as_mut()).await.map_err(|e| e.to_string())?;

        Ok((list, count))
    }

    /**
     * 作者：朱若彪
     * 说明：根据id删除作业
     */
    pub async fn delete_homework(conn: &mut PoolConnection<Postgres>, id: &Uuid) -> Result<Homework, String> {
        let deleted_homework = sqlx::query_as::<_, Homework>("DELETE FROM homework WHERE id = $1 RETURNING *")
            .bind(id)
            .fetch_one(conn.as_mut())
            .await
            .map_err(|e| e.to_string())?;
        Ok(deleted_homework)
    }
    pub async fn fetch_homework_by_id(db: &PgPool, schema_name: &str, id: Uuid) -> Result<Homework, sqlx::error::Error> {
        let homework = sqlx::query_as::<_, Homework>(&format!("SELECT * FROM {}.homework h where h.id = $1", schema_name))
            .bind(id)
            .fetch_one(db)
            .await?;
        Ok(homework)
    }

    fn gen_builder_with_all_homeworks<'a>(is_count: bool, schema_name: &str, name: &'a Option<String>, status: &'a Option<String>, subject_group_id: &'a Option<Uuid>,) -> QueryBuilder<'a, Postgres> {
        let fetch_str = if is_count {"COUNT(*)"} else { "h.*" };
        let mut query_builder = QueryBuilder::new(&format!(
            "SELECT {} FROM {}.homework h WHERE 1=1", fetch_str, schema_name));
        // 添加业务查询条件
        if let Some(name_val) = name {
            if !name_val.trim().is_empty() {
                query_builder.push(" AND h.homework_name LIKE ").push_bind(format!("%{}%", name_val));
            }
        }
        if let Some(status_val) = status {
            query_builder.push(" AND h.homework_status = ").push_bind(status_val);
        }

        if let Some(subject_group_id_val) = subject_group_id {
            query_builder.push(" AND h.subject_group_id = ").push_bind(subject_group_id_val);
        }
        query_builder
    }
    /**
     * 说明：分页查询所有作业（应用数据过滤器）
     * 基于用户权限和角色，动态过滤可访问的作业数据
     */
    pub async fn page_all_homework_with_filter(
        db: &PgPool,
        page_params: &PageParams,
        name: &Option<String>,
        status: &Option<String>,
        subject_group_id: &Option<Uuid>,
        schema_name: &str,
        filter_context: &FilterContext,
        data_filter_manager: &DataFilterManager,
        casbin_service: &dyn CasbinPermissionService
    ) -> anyhow::Result<(Vec<Homework>, i64)> {
        // 构建基础查询语句
        let mut query_builder = Self::gen_builder_with_all_homeworks(true, schema_name, name, status, subject_group_id);
        let count: i64 = query_builder.build_query_scalar().fetch_one(db).await?;
        let mut query_builder = Self::gen_builder_with_all_homeworks(false, schema_name, name, status, subject_group_id);
        // 应用数据过滤器
        data_filter_manager
            .apply_data_query_filter(
                filter_context,
                &mut query_builder,
                casbin_service,
                "h", // 传入作业表的别名
            )
            .await
            .map_err(|e| anyhow::anyhow!("Failed to apply data filter: {}", e))?;
        // 添加分页和排序
        query_builder.push(" ORDER BY h.created_at DESC LIMIT ");
        query_builder.push_bind(page_params.get_limit() as i32);
        query_builder.push(" OFFSET ");
        query_builder.push_bind(page_params.get_offset() as i32);
        debug!("Executing homework query: {}", query_builder.sql());
        // 执行查询
        let list = query_builder.build_query_as::<Homework>().fetch_all(db).await?;
        Ok((list, count))
    }
    fn gen_builder_student_homeworks<'a>(is_count: bool, schema_name: &str) -> QueryBuilder<'a, Postgres> {
        let fetch_str = if is_count {"COUNT(*)"} else { "h.*" };
        let mut builder = QueryBuilder::new(&format!(
            "SELECT {} FROM {}.homework_students hs LEFT JOIN {}.homework h ON hs.homework_id = h.id WHERE 1=1 ", fetch_str, schema_name, schema_name));
        builder
    }
    pub async fn page_student_homeworks(db: &PgPool, schema_name: &str, page_params: &PageParams,
                                        filter_context: &FilterContext,
                                        data_filter_manager: &DataFilterManager,
                                        casbin_service: &dyn CasbinPermissionService) -> anyhow::Result<(Vec<Homework>, i64)> {
        let mut builder = Self::gen_builder_student_homeworks(true, schema_name);
        let count = builder.build_query_scalar().fetch_one(db).await?;
        let mut builder = Self::gen_builder_student_homeworks(false, schema_name);
        // 应用数据过滤器
        data_filter_manager
            .apply_data_query_filter(
                filter_context,
                &mut builder,
                casbin_service,
                "hs", // 传入作业学生表的别名
            )
            .await
            .map_err(|e| anyhow::anyhow!("Failed to apply data filter: {}", e))?;
        builder.push(" ORDER BY h.created_at DESC LIMIT ").push_bind(page_params.get_limit() as i32);
        builder.push(" OFFSET ").push_bind(page_params.get_offset() as i32);
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok((ret, count))
    }

    pub async fn count_by_homework_id(db: &PgPool, filter_context: &FilterContext, data_filter_manager: &DataFilterManager, casbin_service: &dyn CasbinPermissionService) -> anyhow::Result<i64> {
        // 验证输入参数
        if filter_context.schema_name.is_empty() {
            return Err(anyhow::anyhow!("租户域名称不能为空"));
        }

        // 构建基础查询语句
        let mut query_builder = QueryBuilder::new(&format!("SELECT COUNT(*) FROM {}.homework h WHERE 1=1 ", filter_context.schema_name));

        // 应用数据过滤器
        data_filter_manager
            .apply_data_query_filter(
                filter_context,
                &mut query_builder,
                casbin_service,
                "h", // 传入作业表的别名
            )
            .await
            .map_err(|e| anyhow::anyhow!("Failed to apply data filter: {}", e))?;

        // 执行查询
        debug!("Executing homework query: {}", query_builder.sql());

        let count = query_builder.build_query_scalar().fetch_one(db).await?;
        Ok(count)
    }

    pub async fn fetch_homework_leaf_total(
        db: &PgPool,
        filter_context: &FilterContext,
        data_filter_manager: &DataFilterManager,
        casbin_service: &dyn CasbinPermissionService,
    ) -> anyhow::Result<(i64, i64)> {
        // 验证输入参数
        if filter_context.schema_name.is_empty() {
            return Err(anyhow::anyhow!("租户域名称不能为空"));
        }

        // 使用QueryBuilder构建动态查询条件
        let mut query_builder = QueryBuilder::new(format!(
            "SELECT COALESCE(SUM(h.leaf_total), 0) as leaf_total, COALESCE(SUM(h.page_total), 0) as page_total FROM {}.homework h WHERE 1=1",
            filter_context.schema_name
        ));

        // 应用数据过滤器
        data_filter_manager
            .apply_data_query_filter(
                filter_context,
                &mut query_builder,
                casbin_service,
                "h", // 传入作业表的别名
            )
            .await
            .map_err(|e| anyhow!("Failed to apply data filter: {}", e))?;

        // 执行查询
        debug!("Executing query: {}", query_builder.sql());
        let row: (i64, i64) = query_builder.build_query_as().fetch_one(db).await?;

        Ok(row)
    }
}
